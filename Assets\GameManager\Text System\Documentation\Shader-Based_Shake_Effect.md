# Shader-Based Shake Effect Documentation

## Overview

The ShakeAnimationEffect has been successfully modified to use shader-based material property animation instead of mesh vertex manipulation for better performance and visual quality.

## Files Modified/Created

### Modified Files
- `Assets/GameManager/Text System/Text Effects/Text Animation Effect/Continous Effects/ShakeAnimationEffect.cs`

### New Files Created
- `Assets/Shaders/TMP_SDF_Shake.shader` - Custom TextMeshPro shader with shake support
- `Assets/Shaders/TMP_Shake_Material.mat` - Material using the shake shader
- `Assets/GameManager/Text System/Examples/ShakeEffectExample.cs` - Example implementation
- `Assets/GameManager/Text System/Documentation/Shader-Based_Shake_Effect.md` - This documentation

## Key Changes

### 1. ShakeAnimationEffect.cs
- **Removed**: Mesh vertex manipulation code (`_localOriginalVertices`, `CaptureLocalOriginalVertices()`)
- **Added**: Material property-based animation using shader properties
- **Added**: Material reference management for sentence-specific materials
- **Added**: Automatic material instance creation to avoid affecting other text components

### 2. Custom Shader
- **Created**: `TMP_SDF_Shake.shader` - A custom TextMeshPro shader that supports shake effects
- **Properties**: 
  - `_ShakeAmount`: Controls the intensity of the shake effect
  - `_ShakeSpeed`: Controls the speed of the shake animation
  - `_ShakeTime`: Time value for animation progression
- **Features**: Vertex-level shake animation applied in the vertex shader

### 3. Material Setup
- **Created**: `TMP_Shake_Material.mat` - A material using the shake shader
- **Usage**: Can be assigned to `SentenceDisplayData.TextMaterial` for sentences that need shake effects

## How It Works

1. **Material Detection**: The effect checks if the sentence has a material that supports shake properties
2. **Instance Creation**: Creates a material instance to avoid affecting other text components
3. **Property Animation**: Updates shader properties each frame to create the shake effect
4. **Cleanup**: Restores original material and cleans up references when the effect stops

## Usage Instructions

### For Sentences with Custom Materials
1. Create a material using the "TextMeshPro/Distance Field Shake" shader
2. Assign this material to `SentenceDisplayData.TextMaterial`
3. Add `ShakeAnimationEffect` to the sentence's `TextEffects` list

### For Sentences with Default Materials
1. The effect will check if the default material supports shake properties
2. If not, a warning will be logged and the effect will not work
3. Consider creating a default shake material for the TextMeshPro component

## Benefits of Shader-Based Approach

1. **Performance**: No CPU-side vertex manipulation, all animation happens on GPU
2. **Visual Quality**: Smoother animation with sub-pixel precision
3. **Scalability**: Can handle many shake effects simultaneously without performance degradation
4. **Flexibility**: Easy to extend with additional shader-based effects
5. **Compatibility**: Works with all TextMeshPro features (outlines, shadows, etc.)

## Technical Details

### Shader Properties
- `_ShakeAmount` (Range 0-10): Multiplier for shake intensity
- `_ShakeSpeed` (Range 0-100): Frequency of shake oscillation
- `_ShakeTime` (Float): Current time for animation progression

### Material Management
- Each sentence gets its own material instance
- Original materials are preserved and restored
- Automatic cleanup prevents memory leaks

### Fast Forward Support
- Speed multiplier is applied to `_ShakeSpeed` property
- Maintains consistent behavior with other text effects

## Troubleshooting

### "Material does not support shake effect" Warning
- Ensure the material uses the "TextMeshPro/Distance Field Shake" shader
- Check that the material has the required shader properties
- Consider creating a default shake material for the TextMeshPro component

### Shake Effect Not Visible
- Verify `shakeAmount` is greater than 0
- Check that the material is properly assigned
- Ensure the shader is compiled correctly

### Performance Issues
- The shader-based approach should be more performant than vertex manipulation
- If issues persist, check for excessive material instance creation
- Consider using shared materials where appropriate
