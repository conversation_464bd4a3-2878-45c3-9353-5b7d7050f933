{"format": 1, "restore": {"F:\\Huggable X Horror\\Assembly-CSharp-firstpass.csproj": {}}, "projects": {"F:\\Huggable X Horror\\Assembly-CSharp-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Huggable X Horror\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "F:\\Huggable X Horror\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Huggable X Horror\\Temp\\obj\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\Huggable X Horror\\InputHintEditor.csproj": {"projectPath": "F:\\Huggable X Horror\\InputHintEditor.csproj"}, "F:\\Huggable X Horror\\Unity.InputSystem.InGameHints.csproj": {"projectPath": "F:\\Huggable X Horror\\Unity.InputSystem.InGameHints.csproj"}, "F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj": {"projectPath": "F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj"}, "F:\\Huggable X Horror\\XNode.csproj": {"projectPath": "F:\\Huggable X Horror\\XNode.csproj"}, "F:\\Huggable X Horror\\XNodeEditor.csproj": {"projectPath": "F:\\Huggable X Horror\\XNodeEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "F:\\Huggable X Horror\\InputHintEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Huggable X Horror\\InputHintEditor.csproj", "projectName": "InputHintEditor", "projectPath": "F:\\Huggable X Horror\\InputHintEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Huggable X Horror\\Temp\\obj\\InputHintEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "F:\\Huggable X Horror\\Unity.InputSystem.InGameHints.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Huggable X Horror\\Unity.InputSystem.InGameHints.csproj", "projectName": "Unity.InputSystem.InGameHints", "projectPath": "F:\\Huggable X Horror\\Unity.InputSystem.InGameHints.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Huggable X Horror\\Temp\\obj\\Unity.InputSystem.InGameHints\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj", "projectName": "UnityEngine.InputSystem.Samples.UIvsGameInput", "projectPath": "F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Huggable X Horror\\Temp\\obj\\UnityEngine.InputSystem.Samples.UIvsGameInput\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "F:\\Huggable X Horror\\XNode.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Huggable X Horror\\XNode.csproj", "projectName": "XNode", "projectPath": "F:\\Huggable X Horror\\XNode.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Huggable X Horror\\Temp\\obj\\XNode\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "F:\\Huggable X Horror\\XNodeEditor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Huggable X Horror\\XNodeEditor.csproj", "projectName": "XNodeEditor", "projectPath": "F:\\Huggable X Horror\\XNodeEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Huggable X Horror\\Temp\\obj\\XNodeEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\Huggable X Horror\\XNode.csproj": {"projectPath": "F:\\Huggable X Horror\\XNode.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}