using System.Collections;
using TMPro;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
using Unity.EditorCoroutines.Editor;
#endif

[CreateAssetMenu(fileName = "New Shake Effect", menuName = "Game Manager/Text System/Text Animation Effects/Shake Effect")]
public class ShakeAnimationEffect : TextAnimationEffect
{
    [Tooltip("A intensidade do tremor.")]
    public float shakeAmount = 1.0f;
    [Toolt<PERSON>("A velocidade do tremor.")]
    public float speed = 50.0f;

    public override bool IsContinuous => true; // Este efeito deve rodar em paralelo

    // Referência ao material da sentença sendo animada
    private Material _sentenceMaterial;
    private Material _originalMaterial;

    // Propriedades do shader para controle do shake
    private static readonly int ShakeAmountProperty = Shader.PropertyToID("_ShakeAmount");
    private static readonly int ShakeSpeedProperty = Shader.PropertyToID("_ShakeSpeed");
    private static readonly int ShakeTimeProperty = Shader.PropertyToID("_ShakeTime");

    public override IEnumerator Animate(VisualTextDisplayer displayer, SentenceDisplayData sentence, TextMeshProUGUI textComponent, int start, int end, SentenceInfo animData)
    {
        // Configura o material para o efeito de shake
        SetupShakeMaterial(sentence, textComponent);

        // A animação roda enquanto a corrotina de exibição estiver ativa para esta sentença
        while (!_stopRequested)
        {
            // Aplica multiplicador de velocidade se fast forward estiver ativo
            float speedMultiplier = GetSpeedMultiplier();

            // Atualiza as propriedades do shader para criar o efeito de shake
            if (_sentenceMaterial != null)
            {
                _sentenceMaterial.SetFloat(ShakeAmountProperty, shakeAmount);
                _sentenceMaterial.SetFloat(ShakeSpeedProperty, speed * speedMultiplier);
                _sentenceMaterial.SetFloat(ShakeTimeProperty, Time.time);
            }

            #if UNITY_EDITOR
            if (!Application.isPlaying && _sentenceMaterial != null)
                EditorUtility.SetDirty(_sentenceMaterial);
            #endif

            yield return null; // Espera o próximo frame
        }
    }

    public override void Reset(TextMeshProUGUI textComponent, SentenceInfo animData)
    {
        // Restaura o material original e limpa as propriedades do shake
        if (_sentenceMaterial != null)
        {
            _sentenceMaterial.SetFloat(ShakeAmountProperty, 0f);
            _sentenceMaterial.SetFloat(ShakeSpeedProperty, 0f);
            _sentenceMaterial.SetFloat(ShakeTimeProperty, 0f);
        }

        // Restaura o material original se foi alterado
        if (_originalMaterial != null && textComponent != null)
        {
            textComponent.fontSharedMaterial = _originalMaterial;
        }

        // Limpa as referências
        _sentenceMaterial = null;
        _originalMaterial = null;
    }

    /// <summary>
    /// Configura o material para o efeito de shake, criando uma instância se necessário.
    /// </summary>
    private void SetupShakeMaterial(SentenceDisplayData sentence, TextMeshProUGUI textComponent)
    {
        // Armazena o material original
        _originalMaterial = textComponent.fontSharedMaterial;

        // Se a sentença tem um material específico, usa ele; senão usa o material atual do componente
        Material baseMaterial = sentence.TextMaterial != null ? sentence.TextMaterial : _originalMaterial;

        // Verifica se o material já suporta shake (tem as propriedades necessárias)
        if (baseMaterial != null && baseMaterial.HasProperty(ShakeAmountProperty))
        {
            // Cria uma instância do material para não afetar outros textos
            _sentenceMaterial = new Material(baseMaterial);
            textComponent.fontSharedMaterial = _sentenceMaterial;
        }
        else
        {
            Debug.LogWarning($"ShakeAnimationEffect: O material '{baseMaterial?.name}' não suporta efeito de shake. " +
                           "Certifique-se de usar um material com shader que tenha as propriedades _ShakeAmount, _ShakeSpeed e _ShakeTime.");
        }
    }
}