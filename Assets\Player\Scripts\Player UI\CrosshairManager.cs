using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class CrosshairManager : MonoBehaviour
{
    [<PERSON><PERSON>("Crosshair Visual")]
    public Image CrosshairImage;
    public Sprite DefaultSprite;
    public Sprite InteractableSprite;
    
    [Header("Crosshair Text")]
    public TextMeshProUGUI CrosshairText;
    public GameObject CrosshairTextBackground;
    
    [<PERSON><PERSON>("Animation Settings")]
    public float transitionSpeed = 5f;
    
    private bool isShowingInteractable = false;
    
    public VisualTextDisplayer animatedTextDisplayer; // ← Atribua no Inspector

    void Start()
    {
        SetDefaultCrosshair();
    }
    
    public void SetDefaultCrosshair()
    {
        if (CrosshairImage != null && DefaultSprite != null)
        {
            CrosshairImage.sprite = DefaultSprite;
        }
        
        if (CrosshairText != null)
        {
            CrosshairText.text = "";
            CrosshairText.gameObject.SetActive(false);
        }
        
        if (CrosshairTextBackground != null)
        {
            CrosshairTextBackground.SetActive(false);
        }
        
        isShowingInteractable = false;
    }
    
    public void SetInteractableCrosshair(InteractHintData hint)
    {
        Debug.Log("SetInteractableCrosshair");

        if (hint == null)
        {
            SetDefaultCrosshair();
            return;
        }

        // Atualiza sprite do crosshair
        if (CrosshairImage != null)
        {
            Sprite spriteToUse = hint.InteractionSprite != null ? hint.InteractionSprite : InteractableSprite;
            if (spriteToUse != null)
            {
                CrosshairImage.sprite = spriteToUse;
            }
        }

        // Texto puro
        if (!string.IsNullOrEmpty(hint.InteractionText))
        {
            CrosshairText.text = hint.InteractionText;
            CrosshairText.gameObject.SetActive(true);
            if (CrosshairTextBackground != null)
                CrosshairTextBackground.SetActive(true);
        }
        else
        {
            CrosshairText.gameObject.SetActive(false);
            if (CrosshairTextBackground != null)
                CrosshairTextBackground.SetActive(false);
        }

        if (animatedTextDisplayer != null)
        {
            animatedTextDisplayer.StopDisplay();
        }

        isShowingInteractable = true;
    }
    
    public bool IsShowingInteractable()
    {
        return isShowingInteractable;
    }
}
