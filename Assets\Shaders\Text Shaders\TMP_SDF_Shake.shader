Shader "TextMeshPro/Distance Field Shake" {

Properties {
	_FaceColor		    ("Face Color", Color) = (1,1,1,1)
	_FaceDilate			("Face Dilate", Range(-1,1)) = 0

	_OutlineColor	    ("Outline Color", Color) = (0,0,0,1)
	_OutlineWidth		("Outline Thickness", Range(0, 1)) = 0
	_OutlineSoftness	("Outline Softness", Range(0,1)) = 0

	_UnderlayColor	    ("Border Color", Color) = (0,0,0, 0.5)
	_UnderlayOffsetX	("Border OffsetX", Range(-1,1)) = 0
	_UnderlayOffsetY	("Border OffsetY", Range(-1,1)) = 0
	_UnderlayDilate		("Border Dilate", Range(-1,1)) = 0
	_UnderlaySoftness	("Border Softness", Range(0,1)) = 0

	_WeightNormal		("Weight Normal", float) = 0
	_WeightBold			("Weight Bold", float) = 0.5

	_MainTex			("Font Atlas", 2D) = "white" {}
	_TextureWidth		("Texture Width", float) = 512
	_TextureHeight		("Texture Height", float) = 512
	_GradientScale		("Gradient Scale", float) = 5.0
	_ScaleX				("Scale X", float) = 1.0
	_ScaleY				("Scale Y", float) = 1.0
	_PerspectiveFilter	("Perspective Correction", Range(0, 1)) = 0.875
	_Sharpness			("Sharpness", Range(-1,1)) = 0

	_VertexOffsetX		("Vertex OffsetX", float) = 0
	_VertexOffsetY		("Vertex OffsetY", float) = 0

	// Shake Effect Properties
	_ShakeAmount		("Shake Amount", Range(0, 10)) = 0
	_ShakeSpeed			("Shake Speed", Range(0, 100)) = 50
	_ShakeTime			("Shake Time", float) = 0

	_ClipRect			("Clip Rect", vector) = (-32767, -32767, 32767, 32767)
	_MaskSoftnessX		("Mask SoftnessX", float) = 0
	_MaskSoftnessY		("Mask SoftnessY", float) = 0

	_StencilComp		("Stencil Comparison", Float) = 8
	_Stencil			("Stencil ID", Float) = 0
	_StencilOp			("Stencil Operation", Float) = 0
	_StencilWriteMask	("Stencil Write Mask", Float) = 255
	_StencilReadMask	("Stencil Read Mask", Float) = 255

	_ColorMask			("Color Mask", Float) = 15
}

SubShader {

	Tags
	{
		"Queue"="Transparent"
		"IgnoreProjector"="True"
		"RenderType"="Transparent"
	}

	Stencil
	{
		Ref [_Stencil]
		Comp [_StencilComp]
		Pass [_StencilOp]
		ReadMask [_StencilReadMask]
		WriteMask [_StencilWriteMask]
	}

	Cull Off
	ZWrite Off
	Lighting Off
	Fog { Mode Off }
	ZTest [unity_GUIZTestMode]
	Blend One OneMinusSrcAlpha
	ColorMask [_ColorMask]

	Pass {
		CGPROGRAM
		#pragma vertex VertShader
		#pragma fragment PixShader
		#pragma shader_feature __ UNDERLAY_ON UNDERLAY_INNER

		#pragma multi_compile __ UNITY_UI_CLIP_RECT
		#pragma multi_compile __ UNITY_UI_ALPHACLIP

		#include "UnityCG.cginc"
		#include "UnityUI.cginc"

		struct vertex_t {
			UNITY_VERTEX_INPUT_INSTANCE_ID
			float4	vertex			: POSITION;
			float3	normal			: NORMAL;
			fixed4	color			: COLOR;
			float2	texcoord0		: TEXCOORD0;
			float2	texcoord1		: TEXCOORD1;
		};

		struct pixel_t {
			UNITY_VERTEX_INPUT_INSTANCE_ID
			UNITY_VERTEX_OUTPUT_STEREO
			float4	vertex			: SV_POSITION;
			fixed4	faceColor		: COLOR;
			fixed4	outlineColor	: COLOR1;
			float4	texcoord0		: TEXCOORD0;
			half4	param			: TEXCOORD1;
			half4	mask			: TEXCOORD2;
		#if (UNDERLAY_ON | UNDERLAY_INNER)
			float4	texcoord1		: TEXCOORD3;
			half2	underlayParam	: TEXCOORD4;
		#endif
		};

		// Shader properties
		sampler2D _MainTex;
		float4 _MainTex_ST;
		fixed4 _FaceColor;
		fixed4 _OutlineColor;
		fixed4 _UnderlayColor;
		float _FaceDilate;
		float _OutlineWidth;
		float _OutlineSoftness;
		float _UnderlayOffsetX;
		float _UnderlayOffsetY;
		float _UnderlayDilate;
		float _UnderlaySoftness;
		float _WeightNormal;
		float _WeightBold;
		float _TextureWidth;
		float _TextureHeight;
		float _GradientScale;
		float _ScaleX;
		float _ScaleY;
		float _PerspectiveFilter;
		float _Sharpness;
		float _VertexOffsetX;
		float _VertexOffsetY;
		float4 _ClipRect;
		float _MaskSoftnessX;
		float _MaskSoftnessY;

		// Shake effect properties
		float _ShakeAmount;
		float _ShakeSpeed;
		float _ShakeTime;

		pixel_t VertShader(vertex_t input)
		{
			pixel_t output;

			UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);
			UNITY_TRANSFER_INSTANCE_ID(input, output);

			float bold = step(input.texcoord1.y, 0);

			float4 vert = input.vertex;
			vert.x += _VertexOffsetX;
			vert.y += _VertexOffsetY;

			// Apply shake effect to vertex position
			if (_ShakeAmount > 0)
			{
				float shakeX = sin(_ShakeTime * _ShakeSpeed + vert.x * 0.1) * _ShakeAmount * 0.01;
				float shakeY = cos(_ShakeTime * _ShakeSpeed + vert.y * 0.1) * _ShakeAmount * 0.01;
				vert.x += shakeX;
				vert.y += shakeY;
			}

			float4 vPosition = UnityObjectToClipPos(vert);

			float2 pixelSize = vPosition.w;
			pixelSize /= float2(_ScaleX, _ScaleY) * abs(mul((float2x2)UNITY_MATRIX_P, _ScreenParams.xy));

			float scale = rsqrt(dot(pixelSize, pixelSize));
			scale *= abs(input.texcoord1.y) * _GradientScale * (_Sharpness + 1);
			if(UNITY_MATRIX_P[3][3] == 0) scale = lerp(abs(scale) * (1 - _PerspectiveFilter), scale, abs(dot(UnityObjectToWorldNormal(input.normal.xyz), normalize(WorldSpaceViewDir(vert)))));

			float weight = lerp(_WeightNormal, _WeightBold, bold) / 4.0;
			weight = (weight + _FaceDilate) * 0.5;

			float layerScale = scale;

			scale /= 1 + (_OutlineSoftness * scale);
			float bias = (0.5 - weight) * scale - 0.5;
			float outline = _OutlineWidth * 0.5 * scale;

			if (_OutlineWidth > 0)
			{
				scale /= 1 + ((_OutlineSoftness + _OutlineWidth) * scale);
				bias = (0.5 - weight) * scale - 0.5 - outline;
			}

			output.vertex = vPosition;
			output.faceColor = input.color;
			output.faceColor.rgb *= input.color.a;
			output.faceColor *= _FaceColor;
			output.faceColor.rgb /= max(output.faceColor.a, 0.0001);

			output.outlineColor = _OutlineColor;
			output.outlineColor.rgb *= output.outlineColor.a;
			output.outlineColor *= input.color.a;

			output.texcoord0 = float4(input.texcoord0.x, input.texcoord0.y, 0, 0);
			output.param = half4(scale, bias - outline, bias + outline, bias);

			float2 maskUV = vert.xy * 2 - _ClipRect.xy - _ClipRect.zw;
			output.mask = half4(maskUV, 0.25 / (0.25 * half2(_MaskSoftnessX, _MaskSoftnessY) + pixelSize.xy));

		#if (UNDERLAY_ON || UNDERLAY_INNER)
			layerScale /= 1 + ((_UnderlaySoftness + _UnderlayDilate) * layerScale);
			float layerBias = (0.5 - weight) * layerScale - 0.5 - ((_UnderlayDilate + _UnderlaySoftness) * 0.5 * layerScale);

			float x = -(_UnderlayOffsetX) * _GradientScale / _TextureWidth;
			float y = -(_UnderlayOffsetY) * _GradientScale / _TextureHeight;
			output.texcoord1 = float4(input.texcoord0 + float2(x, y), input.color.a, 0);
			output.underlayParam = half2(layerScale, layerBias);
		#endif

			return output;
		}

		fixed4 PixShader(pixel_t input) : SV_Target
		{
			UNITY_SETUP_INSTANCE_ID(input);

			half d = tex2D(_MainTex, input.texcoord0.xy).a * input.param.x;
			half4 c = input.faceColor * saturate(d - input.param.w);

		#ifdef UNDERLAY_ON
			d = tex2D(_MainTex, input.texcoord1.xy).a * input.underlayParam.x;
			c += float4(_UnderlayColor.rgb * _UnderlayColor.a, _UnderlayColor.a) * saturate(d - input.underlayParam.y) * (1 - c.a);
		#endif

		#ifdef UNDERLAY_INNER
			half sd = saturate(d - input.param.z);
			d = tex2D(_MainTex, input.texcoord1.xy).a * input.underlayParam.x;
			c += float4(_UnderlayColor.rgb * _UnderlayColor.a, _UnderlayColor.a) * (1 - saturate(d - input.underlayParam.y)) * sd * (1 - c.a);
		#endif

			// Outline
			if (_OutlineWidth > 0)
			{
				half4 outlineC = input.outlineColor * saturate(d - input.param.y);
				c = lerp(outlineC, c, saturate(d - input.param.z));
			}

			// Alternative implementation to UnityGet2DClipping with support for softness.
		    #if UNITY_UI_CLIP_RECT
			half2 m = saturate((_ClipRect.zw - _ClipRect.xy - abs(input.mask.xy)) * input.mask.zw);
			c *= m.x * m.y;
		    #endif

		    #if UNITY_UI_ALPHACLIP
			clip(c.a - 0.001);
		    #endif

  		    return c;
		}
		ENDCG
	}
}

Fallback "TextMeshPro/Mobile/Distance Field"
}
