using UnityEngine;
using TMPro;

/// <summary>
/// Example script demonstrating how to use the new shader-based text animation effects.
/// This example shows how to set up sentences with shake and wave effects using custom materials.
/// </summary>
public class ShaderBasedTextEffectsExample : MonoBehaviour
{
    [Header("Text System Components")]
    [SerializeField] private VisualTextDisplayer textDisplayer;
    [SerializeField] private TextMeshProUGUI textComponent;

    [Header("Shake Effect Setup")]
    [SerializeField] private Material shakeMaterial; // Assign the TMP_Shake_Material here
    [SerializeField] private ShakeAnimationEffect shakeEffect; // Create a ShakeAnimationEffect asset

    [Header("Wave Effect Setup")]
    [SerializeField] private Material waveMaterial; // Assign the TMP_Wave_Material here
    [SerializeField] private WaveAnimationEffect waveEffect; // Create a WaveAnimationEffect asset

    [Header("Example Settings")]
    [SerializeField] private float shakeIntensity = 2.0f;
    [Serial<PERSON><PERSON>ield] private float shakeSpeed = 30.0f;
    [SerializeField] private float waveHeight = 10.0f;
    [SerializeField] private float waveSpeed = 5.0f;
    [SerializeField] private float waveFrequency = 2.0f;
    
    void Start()
    {
        if (textDisplayer == null)
            textDisplayer = GetComponent<VisualTextDisplayer>();
            
        if (textComponent == null)
            textComponent = GetComponent<TextMeshProUGUI>();
            
        // Example of how to create a sentence with shake effect programmatically
        CreateShakeExampleDocument();
    }
    
    /// <summary>
    /// Creates an example Visual Text Document with shader-based effects
    /// </summary>
    private void CreateShakeExampleDocument()
    {
        // Create a new Visual Text Document
        var document = ScriptableObject.CreateInstance<VisualTextDocument>();

        // Create a phrase with multiple sentences
        var phrase = new PhraseData();

        // Normal sentence without effects
        var normalSentence = new SentenceDisplayData("This is normal text. ");

        // Sentence with shake effect
        var shakeSentence = new SentenceDisplayData("This text is shaking! ");
        shakeSentence.TextMaterial = shakeMaterial; // Assign the shake material

        // Configure the shake effect
        if (shakeEffect != null)
        {
            shakeEffect.shakeAmount = shakeIntensity;
            shakeEffect.speed = shakeSpeed;
            shakeSentence.TextEffects.Add(shakeEffect);
        }

        // Sentence with wave effect
        var waveSentence = new SentenceDisplayData("This text has waves! ");
        waveSentence.TextMaterial = waveMaterial; // Assign the wave material

        // Configure the wave effect
        if (waveEffect != null)
        {
            waveEffect.waveHeight = waveHeight;
            waveEffect.waveSpeed = waveSpeed;
            waveEffect.waveFrequency = waveFrequency;
            waveSentence.TextEffects.Add(waveEffect);
        }

        // Another normal sentence
        var endSentence = new SentenceDisplayData("Back to normal text.");

        // Add sentences to phrase
        phrase.Sentences.Add(normalSentence);
        phrase.Sentences.Add(shakeSentence);
        phrase.Sentences.Add(waveSentence);
        phrase.Sentences.Add(endSentence);

        // Add phrase to document
        document.Phrases.Add(phrase);

        // Display the document
        if (textDisplayer != null)
        {
            StartCoroutine(textDisplayer.DisplayDocumentCoroutine(document));
        }
    }
    
    /// <summary>
    /// Example of how to create shader-based effects at runtime
    /// </summary>
    [ContextMenu("Test Runtime Shake")]
    public void TestRuntimeShake()
    {
        if (textComponent == null || shakeMaterial == null) return;

        // Create a sentence with shake effect
        var sentence = new SentenceDisplayData("Runtime shake test!");
        sentence.TextMaterial = shakeMaterial;

        // Create shake effect instance
        var runtimeShakeEffect = ScriptableObject.CreateInstance<ShakeAnimationEffect>();
        runtimeShakeEffect.shakeAmount = shakeIntensity;
        runtimeShakeEffect.speed = shakeSpeed;

        sentence.TextEffects.Add(runtimeShakeEffect);

        // Apply to text component (simplified example)
        textComponent.text = sentence.Text;

        // In a real scenario, you would use the VisualTextDisplayer system
        Debug.Log("Runtime shake effect created. Use VisualTextDisplayer for full functionality.");
    }

    /// <summary>
    /// Example of how to create a wave effect at runtime
    /// </summary>
    [ContextMenu("Test Runtime Wave")]
    public void TestRuntimeWave()
    {
        if (textComponent == null || waveMaterial == null) return;

        // Create a sentence with wave effect
        var sentence = new SentenceDisplayData("Runtime wave test!");
        sentence.TextMaterial = waveMaterial;

        // Create wave effect instance
        var runtimeWaveEffect = ScriptableObject.CreateInstance<WaveAnimationEffect>();
        runtimeWaveEffect.waveHeight = waveHeight;
        runtimeWaveEffect.waveSpeed = waveSpeed;
        runtimeWaveEffect.waveFrequency = waveFrequency;

        sentence.TextEffects.Add(runtimeWaveEffect);

        // Apply to text component (simplified example)
        textComponent.text = sentence.Text;

        // In a real scenario, you would use the VisualTextDisplayer system
        Debug.Log("Runtime wave effect created. Use VisualTextDisplayer for full functionality.");
    }
    
    /// <summary>
    /// Validates that all required components are assigned
    /// </summary>
    void OnValidate()
    {
        if (shakeMaterial != null && shakeMaterial.shader.name != "TextMeshPro/Distance Field Shake")
        {
            Debug.LogWarning("ShaderBasedTextEffectsExample: The assigned shake material should use the 'TextMeshPro/Distance Field Shake' shader for shake effects to work.");
        }

        if (waveMaterial != null && waveMaterial.shader.name != "TextMeshPro/Distance Field Wave")
        {
            Debug.LogWarning("ShaderBasedTextEffectsExample: The assigned wave material should use the 'TextMeshPro/Distance Field Wave' shader for wave effects to work.");
        }

        if (shakeEffect != null && (shakeEffect.shakeAmount <= 0 || shakeEffect.speed <= 0))
        {
            Debug.LogWarning("ShaderBasedTextEffectsExample: Shake effect should have positive values for shakeAmount and speed.");
        }

        if (waveEffect != null && (waveEffect.waveHeight <= 0 || waveEffect.waveSpeed <= 0))
        {
            Debug.LogWarning("ShaderBasedTextEffectsExample: Wave effect should have positive values for waveHeight and waveSpeed.");
        }
    }
}

/// <summary>
/// Editor helper to create shader-based text effect assets
/// </summary>
#if UNITY_EDITOR
[UnityEditor.CustomEditor(typeof(ShaderBasedTextEffectsExample))]
public class ShaderBasedTextEffectsExampleEditor : UnityEditor.Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        GUILayout.Space(10);
        GUILayout.Label("Create Effect Assets", UnityEditor.EditorStyles.boldLabel);

        if (GUILayout.Button("Create Shake Effect Asset"))
        {
            var shakeEffect = ScriptableObject.CreateInstance<ShakeAnimationEffect>();
            shakeEffect.shakeAmount = 2.0f;
            shakeEffect.speed = 30.0f;

            string path = "Assets/GameManager/Text System/Examples/ExampleShakeEffect.asset";
            UnityEditor.AssetDatabase.CreateAsset(shakeEffect, path);
            UnityEditor.AssetDatabase.SaveAssets();

            Debug.Log($"Created ShakeAnimationEffect asset at: {path}");
        }

        if (GUILayout.Button("Create Wave Effect Asset"))
        {
            var waveEffect = ScriptableObject.CreateInstance<WaveAnimationEffect>();
            waveEffect.waveHeight = 10.0f;
            waveEffect.waveSpeed = 5.0f;
            waveEffect.waveFrequency = 2.0f;

            string path = "Assets/GameManager/Text System/Examples/ExampleWaveEffect.asset";
            UnityEditor.AssetDatabase.CreateAsset(waveEffect, path);
            UnityEditor.AssetDatabase.SaveAssets();

            Debug.Log($"Created WaveAnimationEffect asset at: {path}");
        }

        GUILayout.Space(10);
        GUILayout.Label("Create Materials", UnityEditor.EditorStyles.boldLabel);

        if (GUILayout.Button("Create Shake Material"))
        {
            var shader = Shader.Find("TextMeshPro/Distance Field Shake");
            if (shader != null)
            {
                var material = new Material(shader);
                material.name = "ExampleShakeMaterial";

                string path = "Assets/GameManager/Text System/Examples/ExampleShakeMaterial.mat";
                UnityEditor.AssetDatabase.CreateAsset(material, path);
                UnityEditor.AssetDatabase.SaveAssets();

                Debug.Log($"Created shake material at: {path}");
            }
            else
            {
                Debug.LogError("Could not find 'TextMeshPro/Distance Field Shake' shader. Make sure the shader is compiled correctly.");
            }
        }

        if (GUILayout.Button("Create Wave Material"))
        {
            var shader = Shader.Find("TextMeshPro/Distance Field Wave");
            if (shader != null)
            {
                var material = new Material(shader);
                material.name = "ExampleWaveMaterial";

                string path = "Assets/GameManager/Text System/Examples/ExampleWaveMaterial.mat";
                UnityEditor.AssetDatabase.CreateAsset(material, path);
                UnityEditor.AssetDatabase.SaveAssets();

                Debug.Log($"Created wave material at: {path}");
            }
            else
            {
                Debug.LogError("Could not find 'TextMeshPro/Distance Field Wave' shader. Make sure the shader is compiled correctly.");
            }
        }
    }
}
#endif
