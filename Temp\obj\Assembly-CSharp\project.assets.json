{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"InputHintEditor": "1.0.0", "Unity.InputSystem.InGameHints": "1.0.0", "UnityEngine.InputSystem.Samples.UIvsGameInput": "1.0.0", "XNode": "1.0.0", "XNodeEditor": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}}, "InputHintEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/InputHintEditor.dll": {}}, "runtime": {"bin/placeholder/InputHintEditor.dll": {}}}, "Unity.InputSystem.InGameHints/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Unity.InputSystem.InGameHints.dll": {}}, "runtime": {"bin/placeholder/Unity.InputSystem.InGameHints.dll": {}}}, "UnityEngine.InputSystem.Samples.UIvsGameInput/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/UnityEngine.InputSystem.Samples.UIvsGameInput.dll": {}}, "runtime": {"bin/placeholder/UnityEngine.InputSystem.Samples.UIvsGameInput.dll": {}}}, "XNode/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/XNode.dll": {}}, "runtime": {"bin/placeholder/XNode.dll": {}}}, "XNodeEditor/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"XNode": "1.0.0"}, "compile": {"bin/placeholder/XNodeEditor.dll": {}}, "runtime": {"bin/placeholder/XNodeEditor.dll": {}}}}}, "libraries": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "path": "Assembly-CSharp-firstpass.csproj", "msbuildProject": "Assembly-CSharp-firstpass.csproj"}, "InputHintEditor/1.0.0": {"type": "project", "path": "InputHintEditor.csproj", "msbuildProject": "InputHintEditor.csproj"}, "Unity.InputSystem.InGameHints/1.0.0": {"type": "project", "path": "Unity.InputSystem.InGameHints.csproj", "msbuildProject": "Unity.InputSystem.InGameHints.csproj"}, "UnityEngine.InputSystem.Samples.UIvsGameInput/1.0.0": {"type": "project", "path": "UnityEngine.InputSystem.Samples.UIvsGameInput.csproj", "msbuildProject": "UnityEngine.InputSystem.Samples.UIvsGameInput.csproj"}, "XNode/1.0.0": {"type": "project", "path": "XNode.csproj", "msbuildProject": "XNode.csproj"}, "XNodeEditor/1.0.0": {"type": "project", "path": "XNodeEditor.csproj", "msbuildProject": "XNodeEditor.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp-firstpass >= 1.0.0", "InputHintEditor >= 1.0.0", "Unity.InputSystem.InGameHints >= 1.0.0", "UnityEngine.InputSystem.Samples.UIvsGameInput >= 1.0.0", "XNode >= 1.0.0", "XNodeEditor >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Huggable X Horror\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "F:\\Huggable X Horror\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Huggable X Horror\\Temp\\obj\\Assembly-CSharp\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\Huggable X Horror\\Assembly-CSharp-firstpass.csproj": {"projectPath": "F:\\Huggable X Horror\\Assembly-CSharp-firstpass.csproj"}, "F:\\Huggable X Horror\\InputHintEditor.csproj": {"projectPath": "F:\\Huggable X Horror\\InputHintEditor.csproj"}, "F:\\Huggable X Horror\\Unity.InputSystem.InGameHints.csproj": {"projectPath": "F:\\Huggable X Horror\\Unity.InputSystem.InGameHints.csproj"}, "F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj": {"projectPath": "F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj"}, "F:\\Huggable X Horror\\XNode.csproj": {"projectPath": "F:\\Huggable X Horror\\XNode.csproj"}, "F:\\Huggable X Horror\\XNodeEditor.csproj": {"projectPath": "F:\\Huggable X Horror\\XNodeEditor.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}