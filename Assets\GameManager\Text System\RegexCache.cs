using System;
using System.Collections.Generic;
using System.Text.RegularExpressions;

/// <summary>
/// Cache para expressões regulares compiladas com timeout de segurança.
/// </summary>
public static class RegexCache
{
    private static readonly Dictionary<string, Regex> _cache = new Dictionary<string, Regex>();
    private static readonly TimeSpan _timeout = TimeSpan.FromSeconds(2); // Timeout de 2 segundos

    public static Regex GetRegex(string pattern, RegexOptions options = RegexOptions.None)
    {
        string key = $"{pattern}_{options}";
        
        if (!_cache.TryGetValue(key, out Regex regex))
        {
            try
            {
                regex = new Regex(pattern, options | RegexOptions.Compiled, _timeout);
                _cache[key] = regex;
            }
            catch (ArgumentException ex)
            {
                UnityEngine.Debug.LogError($"Invalid regex pattern: {pattern}. Error: {ex.Message}");
                return null;
            }
        }
        
        return regex;
    }

    public static void ClearCache()
    {
        _cache.Clear();
    }
}