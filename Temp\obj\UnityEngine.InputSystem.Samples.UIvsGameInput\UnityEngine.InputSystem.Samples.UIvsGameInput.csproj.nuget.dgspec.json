{"format": 1, "restore": {"F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj": {}}, "projects": {"F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj", "projectName": "UnityEngine.InputSystem.Samples.UIvsGameInput", "projectPath": "F:\\Huggable X Horror\\UnityEngine.InputSystem.Samples.UIvsGameInput.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Huggable X Horror\\Temp\\obj\\UnityEngine.InputSystem.Samples.UIvsGameInput\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}