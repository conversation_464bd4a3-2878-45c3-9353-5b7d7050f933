using UnityEngine;
using TMPro;

/// <summary>
/// Example script demonstrating how to use the new shader-based ShakeAnimationEffect.
/// This example shows how to set up a sentence with shake effect using the custom material.
/// </summary>
public class ShakeEffectExample : MonoBehaviour
{
    [Header("Text System Components")]
    [SerializeField] private VisualTextDisplayer textDisplayer;
    [SerializeField] private TextMeshProUGUI textComponent;
    
    [Header("Shake Effect Setup")]
    [SerializeField] private Material shakeMaterial; // Assign the TMP_Shake_Material here
    [SerializeField] private ShakeAnimationEffect shakeEffect; // Create a ShakeAnimationEffect asset
    
    [Header("Example Settings")]
    [SerializeField] private float shakeIntensity = 2.0f;
    [SerializeField] private float shakeSpeed = 30.0f;
    
    void Start()
    {
        if (textDisplayer == null)
            textDisplayer = GetComponent<VisualTextDisplayer>();
            
        if (textComponent == null)
            textComponent = GetComponent<TextMeshProUGUI>();
            
        // Example of how to create a sentence with shake effect programmatically
        CreateShakeExampleDocument();
    }
    
    /// <summary>
    /// Creates an example Visual Text Document with shake effects
    /// </summary>
    private void CreateShakeExampleDocument()
    {
        // Create a new Visual Text Document
        var document = ScriptableObject.CreateInstance<VisualTextDocument>();
        
        // Create a phrase with multiple sentences
        var phrase = new PhraseData();
        
        // Normal sentence without shake
        var normalSentence = new SentenceDisplayData("This is normal text. ");
        
        // Sentence with shake effect
        var shakeSentence = new SentenceDisplayData("This text is shaking! ");
        shakeSentence.TextMaterial = shakeMaterial; // Assign the shake material
        
        // Configure the shake effect
        if (shakeEffect != null)
        {
            shakeEffect.shakeAmount = shakeIntensity;
            shakeEffect.speed = shakeSpeed;
            shakeSentence.TextEffects.Add(shakeEffect);
        }
        
        // Another normal sentence
        var endSentence = new SentenceDisplayData("Back to normal text.");
        
        // Add sentences to phrase
        phrase.Sentences.Add(normalSentence);
        phrase.Sentences.Add(shakeSentence);
        phrase.Sentences.Add(endSentence);
        
        // Add phrase to document
        document.Phrases.Add(phrase);
        
        // Display the document
        if (textDisplayer != null)
        {
            StartCoroutine(textDisplayer.DisplayDocumentCoroutine(document));
        }
    }
    
    /// <summary>
    /// Example of how to create a shake effect at runtime
    /// </summary>
    [ContextMenu("Test Runtime Shake")]
    public void TestRuntimeShake()
    {
        if (textComponent == null || shakeMaterial == null) return;
        
        // Create a sentence with shake effect
        var sentence = new SentenceDisplayData("Runtime shake test!");
        sentence.TextMaterial = shakeMaterial;
        
        // Create shake effect instance
        var runtimeShakeEffect = ScriptableObject.CreateInstance<ShakeAnimationEffect>();
        runtimeShakeEffect.shakeAmount = shakeIntensity;
        runtimeShakeEffect.speed = shakeSpeed;
        
        sentence.TextEffects.Add(runtimeShakeEffect);
        
        // Apply to text component (simplified example)
        textComponent.text = sentence.Text;
        
        // In a real scenario, you would use the VisualTextDisplayer system
        Debug.Log("Runtime shake effect created. Use VisualTextDisplayer for full functionality.");
    }
    
    /// <summary>
    /// Validates that all required components are assigned
    /// </summary>
    void OnValidate()
    {
        if (shakeMaterial != null && shakeMaterial.shader.name != "TextMeshPro/Distance Field Shake")
        {
            Debug.LogWarning("ShakeEffectExample: The assigned material should use the 'TextMeshPro/Distance Field Shake' shader for shake effects to work.");
        }
        
        if (shakeEffect != null && (shakeEffect.shakeAmount <= 0 || shakeEffect.speed <= 0))
        {
            Debug.LogWarning("ShakeEffectExample: Shake effect should have positive values for shakeAmount and speed.");
        }
    }
}

/// <summary>
/// Editor helper to create shake effect assets
/// </summary>
#if UNITY_EDITOR
[UnityEditor.CustomEditor(typeof(ShakeEffectExample))]
public class ShakeEffectExampleEditor : UnityEditor.Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("Create Shake Effect Asset"))
        {
            var shakeEffect = ScriptableObject.CreateInstance<ShakeAnimationEffect>();
            shakeEffect.shakeAmount = 2.0f;
            shakeEffect.speed = 30.0f;
            
            string path = "Assets/GameManager/Text System/Examples/ExampleShakeEffect.asset";
            UnityEditor.AssetDatabase.CreateAsset(shakeEffect, path);
            UnityEditor.AssetDatabase.SaveAssets();
            
            Debug.Log($"Created ShakeAnimationEffect asset at: {path}");
        }
        
        if (GUILayout.Button("Create Shake Material"))
        {
            var shader = Shader.Find("TextMeshPro/Distance Field Shake");
            if (shader != null)
            {
                var material = new Material(shader);
                material.name = "ExampleShakeMaterial";
                
                string path = "Assets/GameManager/Text System/Examples/ExampleShakeMaterial.mat";
                UnityEditor.AssetDatabase.CreateAsset(material, path);
                UnityEditor.AssetDatabase.SaveAssets();
                
                Debug.Log($"Created shake material at: {path}");
            }
            else
            {
                Debug.LogError("Could not find 'TextMeshPro/Distance Field Shake' shader. Make sure the shader is compiled correctly.");
            }
        }
    }
}
#endif
