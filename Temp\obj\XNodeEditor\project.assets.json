{"version": 3, "targets": {".NETStandard,Version=v2.1": {"XNode/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/XNode.dll": {}}, "runtime": {"bin/placeholder/XNode.dll": {}}}}}, "libraries": {"XNode/1.0.0": {"type": "project", "path": "XNode.csproj", "msbuildProject": "XNode.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["XNode >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "F:\\Huggable X Horror\\XNodeEditor.csproj", "projectName": "XNodeEditor", "projectPath": "F:\\Huggable X Horror\\XNodeEditor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "F:\\Huggable X Horror\\Temp\\obj\\XNodeEditor\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"F:\\Huggable X Horror\\XNode.csproj": {"projectPath": "F:\\Huggable X Horror\\XNode.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}