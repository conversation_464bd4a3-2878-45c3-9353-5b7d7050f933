using UnityEngine;
using TMPro;
using System.Collections;
using System.Collections.Generic;
using System;
using UnityEngine.InputSystem;
using System.Text;


#if UNITY_EDITOR
using UnityEditor;
using Unity.EditorCoroutines.Editor;
#endif

/// <summary>
/// Controla a exibição de um VisualTextDocument em um componente TextMeshProUGUI.
/// Gerencia o efeito de máquina de escrever, pausas e espera por input do jogador.
/// </summary>
[RequireComponent(typeof(TextMeshProUGUI))]
[ExecuteInEditMode]
public class VisualTextDisplayer : MonoBehaviour
{
    [Tooltip("O componente TextMeshProUGUI onde o texto será exibido.")]
    public TextMeshProUGUI textComponent;

    [Tooltip("O AudioSource usado para reproduzir os efeitos de áudio do texto.")]
    [SerializeField] public AudioSource audioSource;

    public event Action OnDisplayStarted;
    public event Action OnDisplayFinished;
    public event Action<PhraseData> OnPhraseStarted;
    public event Action<PhraseData> OnPhraseFinished;
    public event Action<SentenceDisplayData> OnSentenceStarted;
    public event Action<SentenceDisplayData> OnSentenceFinished;

    private Coroutine _runtimeCoroutine;
    private List<Coroutine> _animationCoroutine = new List<Coroutine>();

    // Armazena os dados de animação de cada sentença para efeitos contínuos
    private Dictionary<SentenceDisplayData, SentenceInfo> _sentenceAnimationDataMap = new Dictionary<SentenceDisplayData, SentenceInfo>();

    // Gerenciador de sentenças para controle avançado
#if UNITY_EDITOR
    public EditorCoroutine _editorCoroutine;
    public List<EditorCoroutine> _editorAnimationCoroutine = new List<EditorCoroutine>();
#endif

    protected bool _skipRequested = false;
    protected bool _waitingForSkip = false;
    protected bool _fastForwardActive = false;

    /// <summary>
    /// Indica se o displayer está atualmente exibindo texto.
    /// </summary>
    public bool IsDisplaying
    {
        get
        {
            return _runtimeCoroutine != null
#if UNITY_EDITOR
                   || _editorCoroutine != null
#endif
                   ;
        }
    }

    [Header("Fast Forward Settings")]
    [Tooltip("Multiplicador de velocidade para o typewriter durante fast forward.")]
    [Range(2f, 20f)]
    public float fastForwardTypewriterMultiplier = 5f;

    [Tooltip("Multiplicador de velocidade para animações durante fast forward.")]
    [Range(2f, 20f)]
    public float fastForwardAnimationMultiplier = 3f;
    private Material _defaultMaterial;
    private TMP_FontAsset _defaultFont;

    void Awake()
    {
        if (textComponent == null)
        {
            textComponent = GetComponent<TextMeshProUGUI>();
        }

        if (textComponent != null)
        {
            _defaultMaterial = textComponent.fontSharedMaterial;
            _defaultFont = textComponent.font;
        }

        if (audioSource == null)
        {
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null)
            {
                audioSource = gameObject.AddComponent<AudioSource>();
            }
        }
    }

    /// <summary>
    /// Inicia a exibição de um documento de texto.
    /// </summary>
    /// <param name="document">O documento a ser exibido.</param>
    public virtual void Display(VisualTextDocument document)
    {
        if (!ValidateDocument(document)) return;
        StartDisplayInternal(document);
    }

#if UNITY_EDITOR
    /// <summary>
    /// Inicia a exibição de um documento de texto no Editor Mode.
    /// </summary>
    /// <param name="document">O documento a ser exibido.</param>
    public virtual void DisplayFromEditor(VisualTextDocument document)
    {
        if (!ValidateDocument(document)) return;
        StartDisplayInternal(document);
    }
#endif

    private void StartDisplayInternal(VisualTextDocument document)
    {
        StopDisplay();
#if UNITY_EDITOR
        if (!Application.isPlaying)
        {
            _editorCoroutine = EditorCoroutineUtility.StartCoroutine(DisplayDocumentCoroutine(document), this);
        }
        else
#endif
        {
            _runtimeCoroutine = StartCoroutine(DisplayDocumentCoroutine(document));
        }
    }

    private bool ValidateDocument(VisualTextDocument document)
    {
        if (document != null && document.Phrases.Count > 0) return true;
        Debug.LogWarning("VisualTextDisplayer: O documento está nulo ou vazio.", this);
        return false;
    }

    /// <summary>
    /// Pula o efeito de máquina de escrever da sentença atual ou avança quando aguardando skip.
    /// </summary>
    public virtual void Skip()
    {
        if (_runtimeCoroutine != null
#if UNITY_EDITOR
            || _editorCoroutine != null
#endif
        )
        {
            _skipRequested = true;
        }

        // Se estiver aguardando skip, permite o avanço
        if (_waitingForSkip)
        {
            _waitingForSkip = false;
        }
    }

    /// <summary>
    /// Ativa ou desativa o modo fast forward.
    /// </summary>
    public virtual void SetFastForward(bool active)
    {
        _fastForwardActive = active;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
        Debug.Log($"Fast Forward {(active ? "ativado" : "desativado")}");
#endif
    }

    /// <summary>
    /// Alterna o estado do fast forward.
    /// </summary>
    public virtual void ToggleFastForward()
    {
        SetFastForward(!_fastForwardActive);
    }

    /// <summary>
    /// Retorna se o fast forward está ativo.
    /// </summary>
    public virtual bool IsFastForwardActive()
    {
        return _fastForwardActive;
    }
    /// <summary>
    /// Para a exibição atual.
    /// </summary>
    public virtual void StopDisplay()
    {
        StopMainCoroutines();
        StopAnimationCoroutines();
        ResetDisplayState();
    }

    private void StopMainCoroutines()
    {
        if (_runtimeCoroutine != null)
        {
            StopCoroutine(_runtimeCoroutine);
            _runtimeCoroutine = null;
        }
#if UNITY_EDITOR
        if (_editorCoroutine != null)
        {
            EditorCoroutineUtility.StopCoroutine(_editorCoroutine);
            _editorCoroutine = null;
        }
#endif
    }

    private void StopAnimationCoroutines()
    {
        for (int i = 0; i < _animationCoroutine.Count; i++)
        {
            if (_animationCoroutine[i] != null)
                StopCoroutine(_animationCoroutine[i]);
        }
        _animationCoroutine.Clear();

#if UNITY_EDITOR
        for (int i = 0; i < _editorAnimationCoroutine.Count; i++)
        {
            if (_editorAnimationCoroutine[i] != null)
                EditorCoroutineUtility.StopCoroutine(_editorAnimationCoroutine[i]);
        }
        _editorAnimationCoroutine.Clear();
#endif
    }

    private void ResetDisplayState()
    {
        _skipRequested = false;
        _waitingForSkip = false;
        
        if (textComponent != null)
        {
            textComponent.text = "";
            textComponent.maxVisibleCharacters = int.MaxValue;
            if (textComponent.textInfo != null && textComponent.textInfo.characterCount > 0)
            {
                textComponent.ForceMeshUpdate(true);
            }
        }

        if (audioSource != null && audioSource.isPlaying)
        {
            audioSource.Stop();
        }

        _sentenceAnimationDataMap.Clear();
    }
    public IEnumerator DisplayDocumentCoroutine(VisualTextDocument document)
    {
        OnDisplayStarted?.Invoke();
        if (textComponent == null)
        {
            textComponent = GetComponent<TextMeshProUGUI>();
        }
        foreach (var phrase in document.Phrases)
        {
            OnPhraseStarted?.Invoke(phrase);
            textComponent.text = ""; // Limpa o texto para a nova frase
            foreach (var sentence in phrase.Sentences)
            {
                // Concatena o texto da nova sentença ao já existente na frase
                string existingText = textComponent.text;
                textComponent.text = string.IsNullOrEmpty(existingText) ? "" : existingText + " ";
                yield return ProcessSentence(sentence);
                yield return AfterSentence(sentence);
            }

            // Para todos os efeitos contínuos da frase quando ela terminar
            Debug.Log("Frase concluida indo para a proxima");
            StopAllContinuousEffectsInPhrase(phrase);

            // Adiciona o delay após a frase, se configurado
            if (phrase.DelayAfterPhrase > 0)
            {
                yield return Application.isPlaying ? new WaitForSeconds(phrase.DelayAfterPhrase) : new WaitForSecondsRealtime(phrase.DelayAfterPhrase);
            }

            // Se a frase requer skip para avançar, aguarda até que skip seja chamado
            if (phrase.RequireSkipToAdvance)
            {
                _waitingForSkip = true;
                Debug.Log($"Aguardando skip para avançar da frase com {phrase.Sentences.Count} sentenças");

                // Aguarda até que skip seja chamado
                while (_waitingForSkip)
                {
                    yield return null;
                }

                Debug.Log("Skip recebido, avançando para a próxima frase");
            }

            OnPhraseFinished?.Invoke(phrase);
        }
        OnDisplayFinished?.Invoke();
    }

    protected virtual IEnumerator ProcessSentence(SentenceDisplayData sentence)
    {
        yield return WriteCoroutine(sentence);
    }

    protected virtual IEnumerator WriteCoroutine(SentenceDisplayData sentence)
    {
        Debug.Log("Processando sentenca 1" + sentence.Text);

        OnSentenceStarted?.Invoke(sentence);
        _skipRequested = false;

        // Aplica o material se houver
        if (sentence.TextMaterial != null)
        {
            textComponent.fontSharedMaterial = sentence.TextMaterial;
        }
        else
        {
            textComponent.fontSharedMaterial = _defaultMaterial;
        }

        // Aplica a fonte se houver
        if (sentence.FontAsset != null)
        {
            textComponent.font = sentence.FontAsset;
        }
        else
        {
            textComponent.font = _defaultFont;
        }

        // Aplica o alinhamento da sentença
        textComponent.alignment = sentence.Alignment;

        // 1. Calcula os índices, adiciona o texto e força a atualização da malha para obter o textInfo correto
        int startCharIndex = textComponent.textInfo.characterCount;
        string formattedText = BuildRichTextForSentence(sentence);
        textComponent.text += formattedText;
        textComponent.ForceMeshUpdate(true); // Crucial para textInfo ser atualizado
        int endCharIndex = textComponent.textInfo.characterCount;

        // Cria dados de animação específicos para esta sentença e os armazena
        SentenceInfo currentSentenceAnimData = new SentenceInfo(textComponent.textInfo);
        currentSentenceAnimData.StartIndex = startCharIndex;
        currentSentenceAnimData.EndIndex = endCharIndex;
        currentSentenceAnimData.originalSentence = sentence;
        _sentenceAnimationDataMap[sentence] = currentSentenceAnimData;

        Debug.Log("Processando sentenca 2" + sentence.Text);
        // 2. Separa os efeitos em contínuos (rodam em paralelo) e sequenciais (bloqueiam a execução)
        var continuousEffects = new List<TextAnimationEffect>();
        var sequentialEffects = new List<TextAnimationEffect>();
        foreach (var effect in sentence.TextEffects)
        {
            Debug.Log("Processando sentenca 3" + sentence.Text);
            if (effect is TextAnimationEffect animEffect)
            {
                if (animEffect.IsContinuous)
                {
                    continuousEffects.Add(animEffect);
                }
                else
                {
                    sequentialEffects.Add(animEffect);
                }
            }
        }

        // 3. Inicia todos os efeitos contínuos em paralelo
        foreach (var effect in continuousEffects)
        {
            Coroutine animCoroutine;
#if UNITY_EDITOR
            if (!Application.isPlaying)
            {
                var editorCoroutine = EditorCoroutineUtility.StartCoroutine(effect.StartEffect(this, sentence, textComponent, startCharIndex, endCharIndex, currentSentenceAnimData), this);
                _editorAnimationCoroutine.Add(editorCoroutine);
            }
            else
#endif
            {
                animCoroutine = StartCoroutine(effect.StartEffect(this, sentence, textComponent, startCharIndex, endCharIndex, currentSentenceAnimData));
                _animationCoroutine.Add(animCoroutine);
            }
        }

        // 4. Executa os efeitos sequenciais ou o efeito de máquina de escrever
        if (sequentialEffects.Count > 0 && !_skipRequested)
        {
            Debug.Log("Processando sentenca 4" + sentence.Text);
            // Para efeitos sequenciais que revelam o texto (como ScaleIn, BlurIn),
            // precisamos garantir que todos os caracteres da sentença sejam renderizáveis.
            // O próprio efeito controlará a visibilidade (escala, alfa, etc.) de cada caractere.
            // O typewriter, por outro lado, depende do maxVisibleCharacters para funcionar.
            textComponent.maxVisibleCharacters = endCharIndex;
        
            foreach (var animEffect in sequentialEffects)
            {
#if UNITY_EDITOR
                if (!Application.isPlaying){
                    yield return EditorCoroutineUtility.StartCoroutine(animEffect.StartEffect(this, sentence, textComponent, startCharIndex, endCharIndex, currentSentenceAnimData), this);
                }
                else{
#endif
                    yield return StartCoroutine(animEffect.StartEffect(this, sentence, textComponent, startCharIndex, endCharIndex, currentSentenceAnimData));
                    if (_skipRequested) break;
                }
            }
        }

        else if (sentence.UseTypewriter && !_skipRequested)
        {
            textComponent.maxVisibleCharacters = startCharIndex;
#if UNITY_EDITOR
            if (!Application.isPlaying)
                yield return EditorCoroutineUtility.StartCoroutine(TypewriterCoroutine(sentence, startCharIndex, endCharIndex), this);
            else
#endif
            yield return StartCoroutine(TypewriterCoroutine(sentence, startCharIndex, endCharIndex));
        }

        // 5. Garante que todo o texto da sentença esteja visível no final (seja por pular ou por concluir)
        textComponent.maxVisibleCharacters = endCharIndex;
        textComponent.ForceMeshUpdate(true);

        if(!Application.isPlaying) EditorUtility.SetDirty(textComponent);

        // 6. Finaliza apenas os efeitos sequenciais, mantendo os contínuos rodando até o fim da frase

        if(sentence.TextEffects != null && sentence.TextEffects.Count > 0)
        {
            foreach (TextEffectBase effect in sentence.TextEffects)
            {
                // Só para e reseta efeitos sequenciais (não contínuos)
                if (effect is TextAnimationEffect animEffect && !animEffect.IsContinuous)
                {
                    effect.StopEffect(); // Sinaliza para a corrotina do efeito parar
                    effect.Reset(textComponent, currentSentenceAnimData);
                }
                // Efeitos contínuos continuam rodando até o fim da frase
            }
        }
        EndSentenceDisplay(sentence);
    }

    protected virtual IEnumerator ClearCoroutine()
    {
        // Futuramente, aqui poderia haver uma animação de fade-out, etc.
        // Por enquanto, limpa instantaneamente.
        textComponent.text = "";
        textComponent.maxVisibleCharacters = 0;
        yield return null;
    }

    protected virtual IEnumerator AfterSentence(SentenceDisplayData sentence)
    {
        // Delay padrão após a sentença
        if (sentence.DelayAfterSentence > 0)
        {
            yield return Application.isPlaying ? new WaitForSeconds(sentence.DelayAfterSentence) : new WaitForSecondsRealtime(sentence.DelayAfterSentence);
        }

        // Se a sentença requer skip para avançar, aguarda até que skip seja chamado
        if (sentence.RequireSkipToAdvance)
        {
            _waitingForSkip = true;
            Debug.Log($"Aguardando skip para avançar da sentença: {sentence.Text}");

            // Aguarda até que skip seja chamado
            while (_waitingForSkip)
            {
                yield return null;
            }

            Debug.Log($"Skip recebido, avançando da sentença: {sentence.Text}");
        }
    }

    protected virtual IEnumerator TypewriterCoroutine(SentenceDisplayData sentence, int start, int end)
    {
        for (int i = start; i < end; i++)
        {
            if (_skipRequested) yield break;

            // Pula espaços em branco para não tocar som ou atrasar
            char currentChar = textComponent.textInfo.characterInfo[i].character;
            if (char.IsWhiteSpace(currentChar))
            {
                textComponent.maxVisibleCharacters = i + 1;
                continue;
            }

            textComponent.maxVisibleCharacters = i + 1;

            // Toca o som de digitação, se houver
            if (sentence.TypewriterSound != null && audioSource != null)
            {
                sentence.TypewriterSound.Play(audioSource);
            }

            yield return GetTypewriterDelay(sentence.DelayPerCharacter);

            if (sentence.PauseOnPunctuation && IsPunctuation(currentChar))
            {
                yield return GetTypewriterDelay(sentence.PunctuationDelay);
            }
        }
    }

    private object GetTypewriterDelay(float baseDelay)
    {
        float delay = _fastForwardActive ? baseDelay / fastForwardTypewriterMultiplier : baseDelay;
        
        if (Application.isPlaying)
        {
            return new WaitForSeconds(delay);
        }
#if UNITY_EDITOR
        else
        {
            EditorUtility.SetDirty(textComponent);
            return new EditorWaitForSeconds(delay);
        }
#endif
        return null;
    }

    private bool IsPunctuation(char c)
    {
        return c == '.' || c == ',' || c == '!' || c == '?' || c == ';' || c == ':';
    }

    protected void EndSentenceDisplay(SentenceDisplayData sentence)
    {
        OnSentenceFinished?.Invoke(sentence);
    }

    public string BuildRichTextForSentence(SentenceDisplayData sentence)
    {
        var sb = new StringBuilder(sentence.Text.Length + 100); // Pre-allocate with estimated size

        // Build opening tags
        string openTags = BuildOpeningTags(sentence);
        sb.Append(openTags);
        sb.Append(sentence.Text);
        
        // Build closing tags (reverse order)
        string closeTags = BuildClosingTags(sentence);
        sb.Append(closeTags);

        return sb.ToString();
    }

    private string BuildOpeningTags(SentenceDisplayData sentence)
    {
        var sb = new StringBuilder(50);
        
        if (!sentence.FollowBaseColor)
            sb.Append($"<color=#{ColorUtility.ToHtmlStringRGB(sentence.TextColor)}>");
        
        sb.Append(!sentence.FollowBaseFontSize ? $"<size={sentence.FontSize}pt>" : "<size=100%>");
        
        if (sentence.IsBold) sb.Append("<b>");
        if (sentence.IsItalic) sb.Append("<i>");
        
        return sb.ToString();
    }

    private string BuildClosingTags(SentenceDisplayData sentence)
    {
        var sb = new StringBuilder(30);
        
        if (sentence.IsItalic) sb.Append("</i>");
        if (sentence.IsBold) sb.Append("</b>");
        sb.Append("</size>");
        if (!sentence.FollowBaseColor) sb.Append("</color>");
        
        return sb.ToString();
    }

    /// <summary>
    /// Para todos os efeitos contínuos de todas as sentenças de uma frase.
    /// </summary>
    protected virtual void StopAllContinuousEffectsInPhrase(PhraseData phrase)
    {
        for (int i = 0; i < phrase.Sentences.Count; i++)
        {
            var sentence = phrase.Sentences[i];
            if (sentence.TextEffects?.Count > 0)
            {
                for (int j = 0; j < sentence.TextEffects.Count; j++)
                {
                    var effect = sentence.TextEffects[j];
                    if (effect is TextAnimationEffect animEffect && animEffect.IsContinuous)
                    {
                        effect.StopEffect();
                    }
                }
            }

            _sentenceAnimationDataMap.Remove(sentence);
        }
    }
}