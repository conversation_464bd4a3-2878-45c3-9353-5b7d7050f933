using System.Collections;
using TMPro;
using UnityEngine;

/// <summary>
/// Classe base abstrata para criar efeitos de animação de texto como ScriptableObjects.
/// Cada efeito de animação deve herdar desta classe e implementar o método Animate.
/// </summary>
public abstract class TextAnimationEffect : TextEffectBase
{
    protected bool _pauseRequested = false;
    protected bool _stopRequested = false;
    protected VisualTextDisplayer _currentDisplayer;

    /// <summary>
    /// O método abstrato que executa a lógica da animação. É uma corrotina para permitir animações ao longo do tempo.
    /// </summary>
    /// <param name="displayer">A referência ao displayer que está executando o efeito.</param>
    /// <param name="sentence">Os dados da sentença que está sendo animada.</param>
    /// <param name="textComponent">O componente TextMeshPro que está exibindo o texto.</param>
    /// <param name="start">O índice do primeiro caractere a ser animado.</param>
    /// <param name="end">O índice do caractere após o último a ser animado.</param>
    /// <param name="animData">Os dados de animação compartilhados para a sentença, como vértices originais.</param>
    public abstract IEnumerator Animate(VisualTextDisplayer displayer, SentenceDisplayData sentence, TextMeshProUGUI textComponent, int start, int end, SentenceInfo animData);

    public override IEnumerator StartEffect(VisualTextDisplayer displayer, SentenceDisplayData sentence, TextMeshProUGUI textComponent, int start, int end, SentenceInfo animData) {
        _stopRequested = false; // Reseta a flag ao iniciar
        _currentDisplayer = displayer; // Armazena referência para acessar fast forward
        yield return Animate(displayer, sentence, textComponent, start, end, animData);
    }

    public override void StopEffect()
    {
        _stopRequested = true;
    }

    public virtual void PauseEffect()
    {
        _pauseRequested = true;
    }

    public virtual void ResumeEffect()
    {
        _pauseRequested = false;
    }

    /// <summary>
    /// Retorna o multiplicador de velocidade baseado no fast forward.
    /// </summary>
    protected float GetSpeedMultiplier()
    {
        if (_currentDisplayer != null && _currentDisplayer.IsFastForwardActive())
        {
            return _currentDisplayer.fastForwardAnimationMultiplier;
        }
        return 1f;
    }

    protected IEnumerator WaitWhilePaused()
    {
        while (_pauseRequested && !_stopRequested)
        {
            yield return null;
        }
    }
}