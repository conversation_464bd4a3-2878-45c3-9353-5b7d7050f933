using UnityEngine;
using System;
using TMPro;
using UnityEngine.Events;
using System.Collections.Generic;

/// <summary>
/// Estrutura de dados para armazenar uma frase e suas configurações de exibição visual.
/// </summary>
[Serializable] // Torna esta classe visível no Inspector do Unity
public class SentenceDisplayData
{
    /// <summary>
    /// Tipos de ação que uma sentença pode executar.
    /// </summary>
    public enum ActionType
    {
        Write,          // Escreve texto normal
        Clear,          // Limpa a tela
        Delete,         // Apaga todo o texto
        DeleteSpecific, // Apaga uma sentença específica por ID
        Replace         // Substitui uma sentença específica
    }

    /// <summary>
    /// Define a ação principal que esta sentença irá executar.
    /// </summary>
    [Tooltip("Tipo de ação que esta sentença executará.")]
    public ActionType Action = ActionType.Write;

    [Tooltip("ID único para controle de eventos. Deixe vazio para desabilitar controle.")]
    public string SentenceID;

    [Tooltip("O texto da frase.")]
    public string Text;

    [Tooltip("Indica se esta sentença foi originada de dentro de chaves {} e, portanto, pode ter configurações visuais personalizadas.")]
    public bool IsFromBraces;

    [Header("Configurações de Exibição (TextMeshPro)")]
    [Tooltip("A cor do texto para esta frase.")]
    public Color TextColor = Color.white; // Cor padrão

    [Tooltip("Utiliza a cor base do TextMeshPro")]
    public bool FollowBaseColor = false;

    [Tooltip("O tamanho da fonte para esta frase.")]
    public float FontSize = 36; // Tamanho padrão, ajuste conforme necessário

    [Tooltip("Utiliza o tamanho da fonte base do TextMeshPro")]
    public bool FollowBaseFontSize = false; // Utiliza o tamanho da fonte base do TextMeshPro

    [Tooltip("Define se o texto desta frase deve ser negrito.")]
    public bool IsBold = false;

    [Tooltip("Define se o texto desta frase deve ser itálico.")]
    public bool IsItalic = false;

    [Tooltip("O alinhamento do texto para esta frase.")]
    public TextAlignmentOptions Alignment = TextAlignmentOptions.Left; // Requer using TMPro

    [Tooltip("A fonte a ser usada para esta sentença. Se nulo, usa a fonte padrão do TextMeshPro.")]
    public TMP_FontAsset FontAsset = null;

    [Header("Configurações de Efeito de Máquina de Escrever")]
    [Tooltip("Se verdadeiro, o texto desta sentença será exibido com um efeito de máquina de escrever.")]
    public bool UseTypewriter = true;

    [Tooltip("O atraso entre a exibição de cada caractere.")]
    [SerializeField]
    public float DelayPerCharacter = 0.05f;

    [Tooltip("Se verdadeiro, o efeito será pausado em caracteres de pontuação (.,!?;).")]
    [SerializeField]
    public bool PauseOnPunctuation = true;

    [Tooltip("O atraso adicional ao encontrar pontuação.")]
    [SerializeField]
    public float PunctuationDelay = 0.2f;

    [Tooltip("O som a ser tocado para cada caractere durante o efeito de máquina de escrever. (Opcional)")]
    public AudioEffect TypewriterSound = null;

    /// <summary>
    /// Configurações de Exibição (Outros)
    /// </summary>

    [Tooltip("O atraso após a conclusão desta sentença antes de prosseguir. Valor padrão é 0.5.")]
    public float DelayAfterSentence = 0.5f;

    [Header("Elementos Dinâmicos")]

    [Tooltip("A imagem (Sprite) a ser exibida (se ShowImage for true).")]
    public Sprite ImageSprite = null; // Requer using UnityEngine

    [Tooltip("Define os efeitos de texto a serem aplicados a esta sentença durante exibição normal.")]
    public List<TextEffectBase> TextEffects = new List<TextEffectBase>();

    [Tooltip("Define os efeitos de texto a serem aplicados durante a animação de delete desta sentença.")]
    public List<TextEffectBase> DeleteEffects = new List<TextEffectBase>();

    [Tooltip("O Material (com shader) a ser aplicado a esta sentença. Se nulo, usa o material padrão.")]
    public Material TextMaterial = null;

    [Header("Eventos")]
    [Tooltip("Eventos a serem disparados no início da exibição desta sentença.")]
    public UnityEvent OnSentenceStart;

    [Tooltip("Eventos a serem disparados no final da exibição desta sentença.")]
    public UnityEvent OnSentenceEnd;

    [Header("Controle de Avanço")]
    [Tooltip("Se verdadeiro, esta sentença só pode avançar para a próxima quando um skip for chamado.")]
    public bool RequireSkipToAdvance = false;

    [Header("Configurações de Ação")]
    [Tooltip("ID da sentença alvo para ações de Delete/Replace.")]
    public string TargetSentenceID;

    [Tooltip("Velocidade de apagar texto (caracteres por segundo).")]
    public float DeleteSpeed = 10f;

    [Tooltip("Som para reproduzir durante delete.")]
    public AudioEffect DeleteSound;

    [Header("Sistema de Replace Inteligente")]
    [Tooltip("Lista de sentenças que podem substituir esta sentença. Navegação automática ou manual.")]
    public List<SentenceDisplayData> ReplacementOptions = new List<SentenceDisplayData>();

    [Tooltip("Índice atual na lista de replacements. -1 = automático (próximo na sequência).")]
    public int CurrentReplacementIndex = -1;

    [Tooltip("Se verdadeiro, volta ao início da lista após o último replacement.")]
    public bool LoopReplacements = false;

    [Tooltip("Delay entre o delete e o replacement (em segundos).")]
    public float ReplaceDelay = 0.5f;

    /// <summary>
    /// Obtém a próxima sentença de replacement baseada no índice atual.
    /// </summary>
    /// <param name="specificIndex">Índice específico ou -1 para automático</param>
    /// <returns>Sentença de replacement ou null se não houver</returns>
    public SentenceDisplayData GetNextReplacement(int specificIndex = -1)
    {
        if (ReplacementOptions?.Count == 0)
            return null;

        int targetIndex = CalculateTargetIndex(specificIndex);
        
        if (targetIndex < 0 || targetIndex >= ReplacementOptions.Count)
            return null;

        CurrentReplacementIndex = targetIndex;
        return ReplacementOptions[targetIndex];
    }

    private int CalculateTargetIndex(int specificIndex)
    {
        if (specificIndex >= 0)
            return specificIndex;

        if (CurrentReplacementIndex < 0)
            return 0;

        int nextIndex = CurrentReplacementIndex + 1;
        if (nextIndex >= ReplacementOptions.Count)
        {
            return LoopReplacements ? 0 : -1;
        }
        
        return nextIndex;
    }

    /// <summary>
    /// Reseta o índice de replacement para o início.
    /// </summary>
    public void ResetReplacementIndex()
    {
        CurrentReplacementIndex = -1;
    }

    /// <summary>
    /// Verifica se há replacements disponíveis.
    /// </summary>
    public bool HasReplacements => ReplacementOptions != null && ReplacementOptions.Count > 0;

    /// <summary>
    /// Obtém o número total de replacements disponíveis.
    /// </summary>
    public int ReplacementCount => ReplacementOptions?.Count ?? 0;

    /// <summary>
    /// Verifica se ainda há replacements disponíveis na sequência.
    /// </summary>
    public bool HasMoreReplacements
    {
        get
        {
            if (ReplacementOptions?.Count > 0)
            {
                return LoopReplacements || CurrentReplacementIndex < ReplacementOptions.Count - 1;
            }
            return false;
        }
    }

    // Construtor para criar um objeto com o texto da frase
    public SentenceDisplayData(string text, bool isFromBraces = false)
    {
        Text = text;
        IsFromBraces = isFromBraces;
        // As outras propriedades usarão seus valores padrão definidos acima
        // Se !isFromBraces, idealmente as configurações visuais permaneceriam padrão,
        // a menos que você tenha um sistema para aplicar estilos diferentes para texto "normal".
    }

    // Construtor vazio necessário para serialização (para aparecer no Inspector)
    public SentenceDisplayData() { }
}